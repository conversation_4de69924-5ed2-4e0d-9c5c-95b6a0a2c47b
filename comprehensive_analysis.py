"""
THE World University Rankings 2026 - Subject Selection Analysis
Comprehensive Analysis and Template Generation for VC Review

Author: Dr. <PERSON><PERSON>, Deputy Director - QMB & Head - QA
Institution: Symbiosis International (Deemed University)
Date: June 23, 2025
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set up professional styling
plt.style.use('seaborn-v0_8-darkgrid')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12
plt.rcParams['axes.titlesize'] = 16
plt.rcParams['axes.titleweight'] = 'bold'

class THESubjectAnalysis:
    """
    Comprehensive analysis class for THE World University Rankings 2026 Subject Selection
    """
    
    def __init__(self, data_file_path, template_file_path):
        self.data_file = data_file_path
        self.template_file = template_file_path
        self.analysis_results = {}
        self.recommendations = {}
        
    def load_data(self):
        """Load all data sheets from SCRI analysis"""
        print("Loading SCRI data...")
        
        # Load all relevant sheets
        self.publications = pd.read_excel(self.data_file, sheet_name='A1_Publications')
        self.journal_quality = pd.read_excel(self.data_file, sheet_name='A2_Journal_Quality')
        self.citations = pd.read_excel(self.data_file, sheet_name='B1_Citations')
        self.high_impact = pd.read_excel(self.data_file, sheet_name='B2_High_Impact')
        self.intl_collab = pd.read_excel(self.data_file, sheet_name='C1_Intl_Collab')
        self.sector_collab = pd.read_excel(self.data_file, sheet_name='C2_Sector_Collab')
        self.staff_strength = pd.read_excel(self.data_file, sheet_name='D1_Staff_Strength')
        self.research_income = pd.read_excel(self.data_file, sheet_name='D2_Research_Income')
        self.patents = pd.read_excel(self.data_file, sheet_name='E1_Patents')
        self.threshold_check = pd.read_excel(self.data_file, sheet_name='F1_Threshold_Check')
        
        print("Data loaded successfully!")
        
    def analyze_subject_performance(self):
        """Comprehensive analysis of each subject's performance"""
        print("Analyzing subject performance...")
        
        # Get subjects that have data (non-null values)
        subjects_with_data = self.publications[
            self.publications['Total Publications'].notna()
        ]['Subject Area'].tolist()
        
        for subject in subjects_with_data:
            self.analysis_results[subject] = self._analyze_single_subject(subject)
            
    def _analyze_single_subject(self, subject):
        """Detailed analysis for a single subject"""
        analysis = {}
        
        # Publications analysis
        pub_data = self.publications[self.publications['Subject Area'] == subject].iloc[0]
        analysis['total_publications'] = pub_data['Total Publications']
        analysis['avg_per_year'] = pub_data['Average/Year']
        analysis['trend'] = pub_data['Trend']
        
        # Journal quality analysis
        jq_data = self.journal_quality[self.journal_quality['Subject Area'] == subject].iloc[0]
        analysis['q1_percentage'] = jq_data['Q1 %']
        analysis['total_papers'] = jq_data['Total']
        
        # Citations analysis
        cit_data = self.citations[self.citations['Subject Area'] == subject].iloc[0]
        analysis['total_citations'] = cit_data['Total Citations']
        analysis['avg_citations_per_paper'] = cit_data['Avg Citations/Paper']
        analysis['h_index'] = cit_data['H-Index']
        analysis['fwci'] = cit_data['FWCI']
        
        # High impact analysis
        hi_data = self.high_impact[self.high_impact['Subject Area'] == subject].iloc[0]
        analysis['top_1_percent'] = hi_data['Top 1% Cited Papers']
        analysis['top_10_percent'] = hi_data['Top 10% Cited Papers']
        analysis['impact_score'] = hi_data['Impact Score']
        
        # International collaboration
        ic_data = self.intl_collab[self.intl_collab['Subject Area'] == subject].iloc[0]
        analysis['intl_collab_percent'] = ic_data['% International']
        analysis['partner_countries'] = ic_data['No. of Countries']
        
        # Threshold check
        tc_data = self.threshold_check[self.threshold_check['Subject Area'] == subject].iloc[0]
        analysis['scri_recommendation'] = tc_data['Recommended for THE?']
        analysis['meets_threshold'] = tc_data['Meets Total Threshold?']
        analysis['meets_100_pubs'] = tc_data['Meets 100 Pubs/Year?']
        
        return analysis
        
    def generate_qmb_recommendations(self):
        """Generate QMB recommendations based on comprehensive analysis"""
        print("Generating QMB recommendations...")
        
        for subject, analysis in self.analysis_results.items():
            recommendation = self._evaluate_subject_for_qmb(subject, analysis)
            self.recommendations[subject] = recommendation
            
    def _evaluate_subject_for_qmb(self, subject, analysis):
        """QMB evaluation criteria for THE ranking suitability"""
        
        # QMB Evaluation Criteria
        criteria_scores = {}
        
        # 1. Publication Volume & Consistency (25%)
        if analysis['avg_per_year'] >= 100:
            criteria_scores['publication_volume'] = 5
        elif analysis['avg_per_year'] >= 75:
            criteria_scores['publication_volume'] = 4
        elif analysis['avg_per_year'] >= 50:
            criteria_scores['publication_volume'] = 3
        else:
            criteria_scores['publication_volume'] = 2
            
        # 2. Research Quality (30%)
        quality_score = 0
        if analysis['q1_percentage'] >= 25:
            quality_score += 2
        elif analysis['q1_percentage'] >= 15:
            quality_score += 1
            
        if analysis['fwci'] >= 1.5:
            quality_score += 2
        elif analysis['fwci'] >= 1.0:
            quality_score += 1
            
        if analysis['h_index'] >= 50:
            quality_score += 1
            
        criteria_scores['research_quality'] = min(quality_score, 5)
        
        # 3. Citation Impact (25%)
        if analysis['avg_citations_per_paper'] >= 10:
            criteria_scores['citation_impact'] = 5
        elif analysis['avg_citations_per_paper'] >= 7:
            criteria_scores['citation_impact'] = 4
        elif analysis['avg_citations_per_paper'] >= 5:
            criteria_scores['citation_impact'] = 3
        else:
            criteria_scores['citation_impact'] = 2
            
        # 4. International Collaboration (20%)
        if analysis['intl_collab_percent'] >= 50:
            criteria_scores['intl_collaboration'] = 5
        elif analysis['intl_collab_percent'] >= 40:
            criteria_scores['intl_collaboration'] = 4
        elif analysis['intl_collab_percent'] >= 30:
            criteria_scores['intl_collaboration'] = 3
        else:
            criteria_scores['intl_collaboration'] = 2
            
        # Calculate weighted score
        weights = {
            'publication_volume': 0.25,
            'research_quality': 0.30,
            'citation_impact': 0.25,
            'intl_collaboration': 0.20
        }
        
        weighted_score = sum(criteria_scores[criterion] * weights[criterion] 
                           for criterion in criteria_scores)
        
        # Generate recommendation
        if weighted_score >= 4.0 and analysis['scri_recommendation'] == 'Yes':
            qmb_recommendation = 'Yes'
            confidence = 'High'
            rationale = f"Strong performance across all metrics. Weighted score: {weighted_score:.2f}/5.0"
        elif weighted_score >= 3.5:
            qmb_recommendation = 'Yes'
            confidence = 'Medium'
            rationale = f"Good performance with room for improvement. Weighted score: {weighted_score:.2f}/5.0"
        elif weighted_score >= 3.0 and analysis['scri_recommendation'] == 'Yes':
            qmb_recommendation = 'Conditional'
            confidence = 'Medium'
            rationale = f"Meets minimum criteria but needs strengthening. Weighted score: {weighted_score:.2f}/5.0"
        else:
            qmb_recommendation = 'No'
            confidence = 'High'
            rationale = f"Below threshold for competitive ranking. Weighted score: {weighted_score:.2f}/5.0"
            
        return {
            'qmb_recommendation': qmb_recommendation,
            'confidence': confidence,
            'weighted_score': weighted_score,
            'criteria_scores': criteria_scores,
            'rationale': rationale,
            'detailed_remarks': self._generate_detailed_remarks(subject, analysis, criteria_scores)
        }
        
    def _generate_detailed_remarks(self, subject, analysis, criteria_scores):
        """Generate detailed QMB remarks for each subject"""

        remarks = []

        # Publication analysis
        if analysis['avg_per_year'] >= 100:
            remarks.append(f"✓ Strong publication output: {analysis['avg_per_year']:.1f} papers/year")
        else:
            remarks.append(f"⚠ Publication output needs improvement: {analysis['avg_per_year']:.1f} papers/year (target: 100+)")

        # Quality analysis
        if analysis['q1_percentage'] >= 20:
            remarks.append(f"✓ Good journal quality: {analysis['q1_percentage']:.1f}% in Q1 journals")
        else:
            remarks.append(f"⚠ Journal quality needs improvement: {analysis['q1_percentage']:.1f}% in Q1 journals")

        # Citation impact
        if analysis['fwci'] >= 1.5:
            remarks.append(f"✓ Excellent citation impact: FWCI = {analysis['fwci']:.2f}")
        elif analysis['fwci'] >= 1.0:
            remarks.append(f"✓ Above-average citation impact: FWCI = {analysis['fwci']:.2f}")
        else:
            remarks.append(f"⚠ Citation impact below field average: FWCI = {analysis['fwci']:.2f}")

        # International collaboration
        if analysis['intl_collab_percent'] >= 40:
            remarks.append(f"✓ Strong international collaboration: {analysis['intl_collab_percent']:.1f}%")
        else:
            remarks.append(f"⚠ International collaboration needs strengthening: {analysis['intl_collab_percent']:.1f}%")

        return " | ".join(remarks)

    def create_vc_template(self):
        """Create the filled template for VC review"""
        print("Creating VC template...")

        # Prepare data for template
        template_data = []

        # Get all subjects from threshold check (includes both recommended and not recommended)
        all_subjects = self.threshold_check['Subject Area'].dropna().tolist()

        for subject in all_subjects:
            if subject in self.analysis_results:
                analysis = self.analysis_results[subject]
                recommendation = self.recommendations[subject]

                # Determine previous year selection (placeholder - would need historical data)
                prev_year_selected = "No"  # Default assumption

                # SCRI recommendation
                scri_rec = analysis.get('scri_recommendation', 'No')

                # SCRI remarks
                scri_remarks = f"Threshold Check: {analysis.get('meets_threshold', 'No')} | " \
                              f"100+ Pubs/Year: {analysis.get('meets_100_pubs', 'No')} | " \
                              f"Total Pubs: {analysis.get('total_publications', 0):.0f} | " \
                              f"Avg/Year: {analysis.get('avg_per_year', 0):.1f}"

                # QMB recommendation and remarks
                qmb_rec = recommendation['qmb_recommendation']
                qmb_remarks = recommendation['detailed_remarks']

                template_data.append({
                    'Subject Area': subject,
                    'Selected for Previous Year (Yes / No)': prev_year_selected,
                    'Recommended by SCRI (yes / No)': scri_rec,
                    'Remarks by SCRI': scri_remarks,
                    'QMB Recommendation (Yes / No)': qmb_rec,
                    'QMB remarks': qmb_remarks,
                    'VC Remarks': ''  # To be filled by VC
                })
            else:
                # For subjects without detailed analysis
                template_data.append({
                    'Subject Area': subject,
                    'Selected for Previous Year (Yes / No)': 'No',
                    'Recommended by SCRI (yes / No)': 'No',
                    'Remarks by SCRI': 'Insufficient data for analysis',
                    'QMB Recommendation (Yes / No)': 'No',
                    'QMB remarks': 'Insufficient publication data to meet THE requirements',
                    'VC Remarks': ''
                })

        # Create DataFrame and save to Excel
        df_template = pd.DataFrame(template_data)

        # Save to new file
        output_file = f'QMB_Analysis_for_VC_{datetime.now().strftime("%Y%m%d")}.xlsx'

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_template.to_excel(writer, sheet_name='QMB_Analysis', index=False)

            # Add summary sheet
            self._create_summary_sheet(writer)

        print(f"Template saved as: {output_file}")
        return output_file

    def _create_summary_sheet(self, writer):
        """Create executive summary sheet"""

        # Count recommendations
        total_subjects = len(self.recommendations)
        yes_recommendations = sum(1 for r in self.recommendations.values() if r['qmb_recommendation'] == 'Yes')
        conditional_recommendations = sum(1 for r in self.recommendations.values() if r['qmb_recommendation'] == 'Conditional')
        no_recommendations = sum(1 for r in self.recommendations.values() if r['qmb_recommendation'] == 'No')

        # Create summary data
        summary_data = [
            ['THE World University Rankings 2026 - Subject Selection Analysis', ''],
            ['Prepared by: Dr. Dharmendra Pandey, Deputy Director - QMB & Head - QA', ''],
            ['Institution: Symbiosis International (Deemed University)', ''],
            ['Analysis Date: ' + datetime.now().strftime("%B %d, %Y"), ''],
            ['', ''],
            ['EXECUTIVE SUMMARY', ''],
            ['', ''],
            ['Total Subjects Analyzed:', total_subjects],
            ['QMB Recommendation - YES:', yes_recommendations],
            ['QMB Recommendation - CONDITIONAL:', conditional_recommendations],
            ['QMB Recommendation - NO:', no_recommendations],
            ['', ''],
            ['RECOMMENDED SUBJECTS FOR THE 2026:', ''],
        ]

        # Add recommended subjects
        for subject, recommendation in self.recommendations.items():
            if recommendation['qmb_recommendation'] in ['Yes', 'Conditional']:
                summary_data.append([f"• {subject}", recommendation['qmb_recommendation']])

        summary_data.extend([
            ['', ''],
            ['KEY FINDINGS:', ''],
            ['• Strong performance in Computer Science and Engineering domains', ''],
            ['• Medicine & Dentistry shows good research output and impact', ''],
            ['• Several subjects need improvement in publication volume', ''],
            ['• International collaboration varies significantly across subjects', ''],
            ['', ''],
            ['RECOMMENDATIONS:', ''],
            ['• Focus resources on subjects with "Yes" recommendations', ''],
            ['• Develop improvement plans for "Conditional" subjects', ''],
            ['• Consider strategic withdrawal from underperforming areas', ''],
            ['• Strengthen international collaborations across all domains', '']
        ])

        df_summary = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
        df_summary.to_excel(writer, sheet_name='Executive_Summary', index=False)

# Initialize and run analysis
if __name__ == "__main__":
    analyzer = THESubjectAnalysis(
        'data by scri.xlsx',
        'QMB Review for VC.xlsx'
    )

    analyzer.load_data()
    analyzer.analyze_subject_performance()
    analyzer.generate_qmb_recommendations()
    output_file = analyzer.create_vc_template()

    print("Analysis completed successfully!")
    print(f"Output file: {output_file}")

    # Print summary for immediate review
    print("\n" + "="*60)
    print("QUICK SUMMARY FOR VC REVIEW")
    print("="*60)

    for subject, recommendation in analyzer.recommendations.items():
        print(f"\n{subject}:")
        print(f"  SCRI Recommendation: {analyzer.analysis_results[subject]['scri_recommendation']}")
        print(f"  QMB Recommendation: {recommendation['qmb_recommendation']}")
        print(f"  Confidence: {recommendation['confidence']}")
        print(f"  Score: {recommendation['weighted_score']:.2f}/5.0")
        print(f"  Key Metrics: Pubs/Year: {analyzer.analysis_results[subject]['avg_per_year']:.1f}, "
              f"FWCI: {analyzer.analysis_results[subject]['fwci']:.2f}, "
              f"Q1%: {analyzer.analysis_results[subject]['q1_percentage']:.1f}%")
