"""
THE World University Rankings 2026 - Visualization Dashboard
Professional visualizations for VC presentation

Author: Dr. <PERSON>, Deputy Director - QMB & Head - QA
Institution: Symbiosis International (Deemed University)
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Professional styling setup
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

# Color scheme for recommendations
COLORS = {
    'Yes': '#2E8B57',      # Sea Green
    'Conditional': '#FF8C00',  # Dark Orange  
    'No': '#DC143C',       # Crimson
    'Primary': '#1f77b4',  # Blue
    'Secondary': '#ff7f0e' # Orange
}

def setup_plot_style():
    """Set consistent styling for all plots"""
    plt.rcParams.update({
        'figure.figsize': (14, 10),
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'legend.fontsize': 12,
        'figure.dpi': 100
    })

def load_analysis_results():
    """Load the analysis results from the generated file"""
    
    # Read the generated analysis file
    df = pd.read_excel('QMB_Analysis_for_VC_20250623.xlsx', sheet_name='QMB_Analysis')
    
    # Also load the original SCRI data for detailed metrics
    scri_data = {}
    scri_data['publications'] = pd.read_excel('data by scri.xlsx', sheet_name='A1_Publications')
    scri_data['journal_quality'] = pd.read_excel('data by scri.xlsx', sheet_name='A2_Journal_Quality')
    scri_data['citations'] = pd.read_excel('data by scri.xlsx', sheet_name='B1_Citations')
    scri_data['high_impact'] = pd.read_excel('data by scri.xlsx', sheet_name='B2_High_Impact')
    scri_data['intl_collab'] = pd.read_excel('data by scri.xlsx', sheet_name='C1_Intl_Collab')
    
    return df, scri_data

def create_recommendation_overview(df):
    """Create overview of QMB recommendations"""
    setup_plot_style()
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('THE 2026 Subject Selection - QMB Analysis Overview', fontsize=20, fontweight='bold', y=0.98)
    
    # 1. Recommendation distribution (pie chart)
    rec_counts = df['QMB Recommendation (Yes / No)'].value_counts()
    colors_pie = [COLORS[rec] for rec in rec_counts.index]
    
    wedges, texts, autotexts = ax1.pie(rec_counts.values, labels=rec_counts.index, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90, textprops={'fontsize': 12})
    ax1.set_title('QMB Recommendation Distribution', fontweight='bold', pad=20)
    
    # 2. SCRI vs QMB recommendations comparison
    comparison_data = df.groupby(['Recommended by SCRI (yes / No)', 'QMB Recommendation (Yes / No)']).size().unstack(fill_value=0)
    comparison_data.plot(kind='bar', ax=ax2, color=[COLORS['No'], COLORS['Conditional'], COLORS['Yes']])
    ax2.set_title('SCRI vs QMB Recommendations', fontweight='bold', pad=20)
    ax2.set_xlabel('SCRI Recommendation')
    ax2.set_ylabel('Number of Subjects')
    ax2.legend(title='QMB Recommendation', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.tick_params(axis='x', rotation=0)
    
    # 3. Subject areas by recommendation (horizontal bar)
    subject_rec = df.set_index('Subject Area')['QMB Recommendation (Yes / No)']
    colors_bar = [COLORS[rec] for rec in subject_rec.values]
    
    y_pos = np.arange(len(subject_rec))
    ax3.barh(y_pos, [1]*len(subject_rec), color=colors_bar, alpha=0.8)
    ax3.set_yticks(y_pos)
    ax3.set_yticklabels([s.replace(': ', ':\n') for s in subject_rec.index], fontsize=10)
    ax3.set_xlabel('Subjects')
    ax3.set_title('Subject-wise QMB Recommendations', fontweight='bold', pad=20)
    ax3.set_xlim(0, 1.2)
    
    # Add recommendation labels
    for i, rec in enumerate(subject_rec.values):
        ax3.text(1.05, i, rec, va='center', fontweight='bold', fontsize=10)
    
    # 4. Summary statistics
    ax4.axis('off')
    
    # Calculate summary stats
    total_subjects = len(df)
    yes_count = len(df[df['QMB Recommendation (Yes / No)'] == 'Yes'])
    conditional_count = len(df[df['QMB Recommendation (Yes / No)'] == 'Conditional'])
    no_count = len(df[df['QMB Recommendation (Yes / No)'] == 'No'])
    
    scri_yes = len(df[df['Recommended by SCRI (yes / No)'] == 'Yes'])
    
    summary_text = f"""
    EXECUTIVE SUMMARY
    
    Total Subjects Analyzed: {total_subjects}
    
    QMB RECOMMENDATIONS:
    ✓ YES: {yes_count} subjects ({yes_count/total_subjects*100:.1f}%)
    ⚠ CONDITIONAL: {conditional_count} subjects ({conditional_count/total_subjects*100:.1f}%)
    ✗ NO: {no_count} subjects ({no_count/total_subjects*100:.1f}%)
    
    SCRI Recommendations: {scri_yes} subjects
    
    RECOMMENDED FOR THE 2026:
    {chr(10).join(['• ' + row['Subject Area'] for _, row in df.iterrows() 
                   if row['QMB Recommendation (Yes / No)'] in ['Yes', 'Conditional']])}
    
    Analysis Date: {datetime.now().strftime("%B %d, %Y")}
    Prepared by: Dr. Dharmendra Pandey
    Deputy Director - QMB & Head - QA
    """
    
    ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('THE_2026_QMB_Overview.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_performance_metrics_dashboard(df, scri_data):
    """Create detailed performance metrics dashboard"""
    setup_plot_style()
    
    # Merge data for analysis
    subjects_with_data = scri_data['publications'][scri_data['publications']['Total Publications'].notna()]
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('Research Performance Metrics - THE 2026 Subject Analysis', fontsize=20, fontweight='bold', y=0.98)
    
    # 1. Publications per year vs FWCI
    pub_data = scri_data['publications'].merge(scri_data['citations'], on='Subject Area')
    pub_data = pub_data[pub_data['Average/Year'].notna() & pub_data['FWCI'].notna()]
    
    # Color by QMB recommendation
    pub_data_merged = pub_data.merge(df[['Subject Area', 'QMB Recommendation (Yes / No)']], on='Subject Area', how='left')
    colors_scatter = [COLORS.get(rec, 'gray') for rec in pub_data_merged['QMB Recommendation (Yes / No)']]
    
    scatter = ax1.scatter(pub_data_merged['Average/Year'], pub_data_merged['FWCI'], 
                         c=colors_scatter, s=100, alpha=0.7, edgecolors='black', linewidth=1)
    
    # Add threshold lines
    ax1.axvline(x=100, color='red', linestyle='--', alpha=0.7, label='Min Publications (100/year)')
    ax1.axhline(y=1.0, color='blue', linestyle='--', alpha=0.7, label='World Average FWCI (1.0)')
    
    ax1.set_xlabel('Average Publications per Year')
    ax1.set_ylabel('Field-Weighted Citation Impact (FWCI)')
    ax1.set_title('Publication Volume vs Citation Impact', fontweight='bold', pad=20)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Add subject labels for key points
    for _, row in pub_data_merged.iterrows():
        if row['Average/Year'] > 200 or row['FWCI'] > 2.0:
            ax1.annotate(row['Subject Area'].split(':')[-1].strip(), 
                        (row['Average/Year'], row['FWCI']),
                        xytext=(5, 5), textcoords='offset points', fontsize=9)
    
    # 2. Journal Quality Distribution
    jq_data = scri_data['journal_quality'][scri_data['journal_quality']['Q1 %'].notna()]
    jq_data_merged = jq_data.merge(df[['Subject Area', 'QMB Recommendation (Yes / No)']], on='Subject Area', how='left')
    
    # Group by recommendation and plot Q1 percentages
    for rec in ['Yes', 'Conditional', 'No']:
        data_subset = jq_data_merged[jq_data_merged['QMB Recommendation (Yes / No)'] == rec]
        if len(data_subset) > 0:
            ax2.scatter(range(len(data_subset)), data_subset['Q1 %'], 
                       label=f'QMB: {rec}', color=COLORS[rec], s=100, alpha=0.7)
    
    ax2.axhline(y=20, color='red', linestyle='--', alpha=0.7, label='Target Q1% (20%)')
    ax2.set_xlabel('Subject Index')
    ax2.set_ylabel('Q1 Journal Percentage (%)')
    ax2.set_title('Journal Quality by QMB Recommendation', fontweight='bold', pad=20)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. International Collaboration Analysis
    ic_data = scri_data['intl_collab'][scri_data['intl_collab']['% International'].notna()]
    ic_data_merged = ic_data.merge(df[['Subject Area', 'QMB Recommendation (Yes / No)']], on='Subject Area', how='left')
    
    # Box plot by recommendation
    rec_groups = []
    rec_labels = []
    for rec in ['Yes', 'Conditional', 'No']:
        data_subset = ic_data_merged[ic_data_merged['QMB Recommendation (Yes / No)'] == rec]['% International']
        if len(data_subset) > 0:
            rec_groups.append(data_subset)
            rec_labels.append(f'{rec}\n(n={len(data_subset)})')
    
    bp = ax3.boxplot(rec_groups, labels=rec_labels, patch_artist=True)
    for patch, rec in zip(bp['boxes'], ['Yes', 'Conditional', 'No']):
        patch.set_facecolor(COLORS[rec])
        patch.set_alpha(0.7)
    
    ax3.axhline(y=40, color='red', linestyle='--', alpha=0.7, label='Target Intl Collab (40%)')
    ax3.set_ylabel('International Collaboration (%)')
    ax3.set_title('International Collaboration by QMB Recommendation', fontweight='bold', pad=20)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Key Performance Indicators Summary
    ax4.axis('off')

    # Calculate key statistics
    recommended_subjects = df[df['QMB Recommendation (Yes / No)'] == 'Yes']

    if len(recommended_subjects) > 0:
        # Get performance data for recommended subjects
        perf_summary = []
        for _, row in recommended_subjects.iterrows():
            subject = row['Subject Area']
            pub_row = scri_data['publications'][scri_data['publications']['Subject Area'] == subject]
            cit_row = scri_data['citations'][scri_data['citations']['Subject Area'] == subject]
            jq_row = scri_data['journal_quality'][scri_data['journal_quality']['Subject Area'] == subject]

            if not pub_row.empty and not cit_row.empty:
                perf_summary.append({
                    'Subject': subject.split(':')[-1].strip(),
                    'Pubs/Year': pub_row['Average/Year'].iloc[0],
                    'FWCI': cit_row['FWCI'].iloc[0],
                    'Q1%': jq_row['Q1 %'].iloc[0] if not jq_row.empty else 0
                })

        # Create summary text
        summary_text = "KEY PERFORMANCE INDICATORS\nRecommended Subjects (QMB: YES)\n\n"
        for item in perf_summary:
            summary_text += f"• {item['Subject']}\n"
            summary_text += f"  Publications/Year: {item['Pubs/Year']:.1f}\n"
            summary_text += f"  FWCI: {item['FWCI']:.2f}\n"
            summary_text += f"  Q1 Journals: {item['Q1%']:.1f}%\n\n"

        summary_text += "\nSTRENGTHS:\n"
        summary_text += "• Strong publication volumes\n"
        summary_text += "• Above-average citation impact\n"
        summary_text += "• Good international visibility\n\n"

        summary_text += "AREAS FOR IMPROVEMENT:\n"
        summary_text += "• Journal quality enhancement\n"
        summary_text += "• International collaboration\n"
        summary_text += "• Research impact optimization"

        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('THE_2026_Performance_Metrics.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # Load data
    df, scri_data = load_analysis_results()
    
    # Create visualizations
    print("Creating recommendation overview...")
    create_recommendation_overview(df)
    
    print("Creating performance metrics dashboard...")
    create_performance_metrics_dashboard(df, scri_data)
    
    print("Visualizations created successfully!")
    print("Files saved:")
    print("- THE_2026_QMB_Overview.png")
    print("- THE_2026_Performance_Metrics.png")
