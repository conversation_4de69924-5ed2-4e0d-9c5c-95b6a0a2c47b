"""
Fill the original QMB Review for VC.xlsx template with analysis findings
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from datetime import datetime

def fill_qmb_template():
    """Fill the original QMB template with comprehensive analysis"""
    
    print("Loading analysis results...")
    
    # Load the comprehensive analysis results
    df_analysis = pd.read_excel('QMB_Analysis_for_VC_20250623.xlsx', sheet_name='QMB_Analysis')
    
    # Load the original SCRI data for additional context
    scri_data = {}
    scri_data['publications'] = pd.read_excel('data by scri.xlsx', sheet_name='A1_Publications')
    scri_data['citations'] = pd.read_excel('data by scri.xlsx', sheet_name='B1_Citations')
    scri_data['journal_quality'] = pd.read_excel('data by scri.xlsx', sheet_name='A2_Journal_Quality')
    scri_data['threshold'] = pd.read_excel('data by scri.xlsx', sheet_name='F1_Threshold_Check')
    
    print("Opening original template...")
    
    # Load the original template
    wb = openpyxl.load_workbook('QMB Review for VC.xlsx')
    ws = wb['Analysis']
    
    # Clear existing data except headers (keep row 1)
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        for cell in row:
            cell.value = None
    
    print("Filling template with analysis data...")
    
    # Get all subjects that have actual data (not just empty rows)
    subjects_with_data = []
    
    # Get subjects from publications data (these have actual research data)
    pub_subjects = scri_data['publications'][scri_data['publications']['Total Publications'].notna()]['Subject Area'].tolist()
    
    # Get subjects from threshold check (includes all subjects SCRI evaluated)
    threshold_subjects = scri_data['threshold']['Subject Area'].dropna().tolist()
    
    # Combine and deduplicate
    all_subjects = list(set(pub_subjects + threshold_subjects))
    
    # Fill the template row by row
    row_idx = 2
    
    for subject in all_subjects:
        # Get data for this subject
        subject_data = get_subject_data(subject, scri_data, df_analysis)
        
        # Fill the row
        ws[f'A{row_idx}'] = subject
        ws[f'B{row_idx}'] = subject_data['prev_year_selected']
        ws[f'C{row_idx}'] = subject_data['scri_recommendation']
        ws[f'D{row_idx}'] = subject_data['scri_remarks']
        ws[f'E{row_idx}'] = subject_data['qmb_recommendation']
        ws[f'F{row_idx}'] = subject_data['qmb_remarks']
        ws[f'G{row_idx}'] = ''  # VC Remarks - to be filled by VC
        
        row_idx += 1
    
    print("Applying professional formatting...")
    
    # Apply formatting
    apply_formatting(ws, row_idx - 1)
    
    # Save the filled template
    wb.save('QMB Review for VC.xlsx')
    
    print(f"✅ Successfully filled the original QMB Review for VC.xlsx template!")
    print(f"📊 Total subjects filled: {row_idx - 2}")
    print(f"📅 Template ready for VC review and decision")
    
    return row_idx - 2

def get_subject_data(subject, scri_data, df_analysis):
    """Get comprehensive data for a specific subject"""
    
    # Initialize default values
    data = {
        'prev_year_selected': 'No',  # Default assumption
        'scri_recommendation': 'No',
        'scri_remarks': 'No data available',
        'qmb_recommendation': 'No',
        'qmb_remarks': 'Insufficient data for analysis'
    }
    
    # Get SCRI recommendation from threshold check
    threshold_row = scri_data['threshold'][scri_data['threshold']['Subject Area'] == subject]
    if not threshold_row.empty:
        scri_rec = threshold_row['Recommended for THE?'].iloc[0]
        data['scri_recommendation'] = 'Yes' if scri_rec == 'Yes' else 'No'
        
        # Build SCRI remarks
        meets_threshold = threshold_row['Meets Total Threshold?'].iloc[0] if 'Meets Total Threshold?' in threshold_row.columns else 'Unknown'
        meets_100_pubs = threshold_row['Meets 100 Pubs/Year?'].iloc[0] if 'Meets 100 Pubs/Year?' in threshold_row.columns else 'Unknown'
        
        scri_remarks_parts = [f"SCRI Recommendation: {scri_rec}"]
        if meets_threshold != 'Unknown':
            scri_remarks_parts.append(f"Meets Threshold: {meets_threshold}")
        if meets_100_pubs != 'Unknown':
            scri_remarks_parts.append(f"100+ Pubs/Year: {meets_100_pubs}")
    
    # Get publication data
    pub_row = scri_data['publications'][scri_data['publications']['Subject Area'] == subject]
    if not pub_row.empty:
        total_pubs = pub_row['Total Publications'].iloc[0]
        avg_per_year = pub_row['Average/Year'].iloc[0]
        if pd.notna(total_pubs) and pd.notna(avg_per_year):
            scri_remarks_parts.append(f"Total Pubs: {total_pubs:.0f}")
            scri_remarks_parts.append(f"Avg/Year: {avg_per_year:.1f}")
    
    # Get citation data
    cit_row = scri_data['citations'][scri_data['citations']['Subject Area'] == subject]
    if not cit_row.empty:
        fwci = cit_row['FWCI'].iloc[0]
        h_index = cit_row['H-Index'].iloc[0]
        if pd.notna(fwci):
            scri_remarks_parts.append(f"FWCI: {fwci:.2f}")
        if pd.notna(h_index):
            scri_remarks_parts.append(f"H-Index: {h_index:.0f}")
    
    # Get journal quality data
    jq_row = scri_data['journal_quality'][scri_data['journal_quality']['Subject Area'] == subject]
    if not jq_row.empty:
        q1_percent = jq_row['Q1 %'].iloc[0]
        if pd.notna(q1_percent):
            scri_remarks_parts.append(f"Q1%: {q1_percent:.1f}%")
    
    if 'scri_remarks_parts' in locals():
        data['scri_remarks'] = " | ".join(scri_remarks_parts)
    
    # Get QMB analysis if available
    qmb_row = df_analysis[df_analysis['Subject Area'] == subject]
    if not qmb_row.empty:
        data['qmb_recommendation'] = qmb_row['QMB Recommendation (Yes / No)'].iloc[0]
        data['qmb_remarks'] = qmb_row['QMB remarks'].iloc[0]
    else:
        # Generate QMB remarks based on available data
        if not pub_row.empty and pd.notna(pub_row['Average/Year'].iloc[0]):
            avg_pubs = pub_row['Average/Year'].iloc[0]
            if avg_pubs < 50:
                data['qmb_remarks'] = f"Below minimum publication threshold ({avg_pubs:.1f} pubs/year). Not suitable for THE ranking."
            elif avg_pubs < 100:
                data['qmb_remarks'] = f"Moderate publication output ({avg_pubs:.1f} pubs/year). Needs improvement to meet THE standards."
            else:
                data['qmb_remarks'] = f"Good publication volume ({avg_pubs:.1f} pubs/year). Requires detailed quality assessment."
    
    return data

def apply_formatting(ws, max_row):
    """Apply professional formatting to the worksheet"""
    
    # Define styles
    header_font = Font(bold=True, color='FFFFFF', size=12)
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    
    yes_fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')  # Light green
    conditional_fill = PatternFill(start_color='FFEB9C', end_color='FFEB9C', fill_type='solid')  # Light yellow
    no_fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # Light red
    
    # Format headers
    for col in range(1, 8):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # Format data rows
    for row_idx in range(2, max_row + 1):
        qmb_rec = ws[f'E{row_idx}'].value
        
        # Determine fill color based on QMB recommendation
        if qmb_rec == 'Yes':
            fill = yes_fill
        elif qmb_rec == 'Conditional':
            fill = conditional_fill
        else:
            fill = no_fill
        
        # Apply formatting to all columns in the row
        for col in range(1, 8):
            cell = ws.cell(row=row_idx, column=col)
            cell.fill = fill
            cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
            
            # Make QMB recommendation column bold
            if col == 5:  # QMB Recommendation column
                cell.font = Font(bold=True)
    
    # Adjust column widths for better readability
    column_widths = [45, 18, 18, 55, 18, 65, 25]
    for idx, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(idx)].width = width
    
    # Add borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.iter_rows(min_row=1, max_row=max_row, min_col=1, max_col=7):
        for cell in row:
            cell.border = thin_border
    
    # Set row heights for better readability
    for row_idx in range(2, max_row + 1):
        ws.row_dimensions[row_idx].height = 60  # Increased height for wrapped text

if __name__ == "__main__":
    try:
        total_subjects = fill_qmb_template()
        print(f"\n🎉 Template filling completed successfully!")
        print(f"📋 {total_subjects} subjects analyzed and filled")
        print(f"📁 File: QMB Review for VC.xlsx")
        print(f"✅ Ready for VC review and final decision")
        
    except Exception as e:
        print(f"❌ Error filling template: {e}")
        import traceback
        traceback.print_exc()
